import axios, { AxiosInstance, AxiosResponse } from "axios";
import {
  BookamatConfig,
  BookamatApiError,
  ApiResponse,
  BankAccount,
  CostAccount,
  PurchaseTaxAccount,
  CostCenter,
  Booking,
  CreateBooking,
} from "./types.js";

export class BookamatApiClient {
  private client: AxiosInstance;
  private config: BookamatConfig;

  constructor(config: BookamatConfig) {
    this.config = config;

    const baseURL = `${config.baseUrl}/api/${config.apiVersion}/${config.country}/${config.year}/`;

    this.client = axios.create({
      baseURL,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `ApiKey ${config.username}:${config.apiKey}`,
      },
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          throw new BookamatApiError(
            `API Error: ${error.response.status} - ${error.response.statusText}`,
            error.response.status,
            error.response.data
          );
        } else if (error.request) {
          throw new BookamatApiError("Network Error: No response received");
        } else {
          throw new BookamatApiError(`Request Error: ${error.message}`);
        }
      }
    );
  }

  // Bank Accounts (Zahlungsmittelkonten)
  async getBankAccounts(params?: {
    page?: number;
    ordering?: string;
    has_bookings?: boolean;
  }): Promise<ApiResponse<BankAccount>> {
    const response = await this.client.get("preferences/bankaccounts/", {
      params,
    });
    return response.data;
  }

  async getBankAccount(id: number): Promise<BankAccount> {
    const response = await this.client.get(`preferences/bankaccounts/${id}/`);
    return response.data;
  }

  // Cost Accounts (Steuerkonten)
  async getCostAccounts(params?: {
    page?: number;
    costaccount?: number;
    group?: string;
    inventory?: boolean;
    active?: boolean;
    has_bookings?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<CostAccount>> {
    const response = await this.client.get("preferences/costaccounts/", {
      params,
    });
    return response.data;
  }

  async getCostAccount(id: number): Promise<CostAccount> {
    const response = await this.client.get(`preferences/costaccounts/${id}/`);
    return response.data;
  }

  // Purchase Tax Accounts (Umsatzsteuerkonten)
  async getPurchaseTaxAccounts(params?: {
    page?: number;
    purchasetaxaccount?: number;
    group?: string;
    reverse_charge?: boolean;
    ic_report?: boolean;
    ic_delivery?: boolean;
    ic_service?: boolean;
    ioss_report?: boolean;
    eu_oss_report?: boolean;
    active?: boolean;
    has_bookings?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<PurchaseTaxAccount>> {
    const response = await this.client.get("preferences/purchasetaxaccounts/", {
      params,
    });
    return response.data;
  }

  async getPurchaseTaxAccount(id: number): Promise<PurchaseTaxAccount> {
    const response = await this.client.get(
      `preferences/purchasetaxaccounts/${id}/`
    );
    return response.data;
  }

  // Cost Centers (Kostenstellen)
  async getCostCenters(params?: {
    page?: number;
    has_bookings?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<CostCenter>> {
    const response = await this.client.get("preferences/costcentres/", {
      params,
    });
    return response.data;
  }

  async getCostCenter(id: number): Promise<CostCenter> {
    const response = await this.client.get(`preferences/costcentres/${id}/`);
    return response.data;
  }

  // =============================================================================
  // BOOKINGS API METHODS
  // =============================================================================

  async getBookings(params?: {
    page?: number;
    group?: string; // "1" for income, "2" for expenses
    title?: string;
    title_contains?: string;
    date?: string;
    date_from?: string;
    date_until?: string;
    date_invoice?: string;
    date_invoice_from?: string;
    date_invoice_until?: string;
    date_delivery?: string;
    date_delivery_from?: string;
    date_delivery_until?: string;
    date_order?: string;
    date_order_from?: string;
    date_order_until?: string;
    amount?: string;
    amount_min?: string;
    amount_max?: string;
    amount_after_tax?: string;
    amount_after_tax_min?: string;
    amount_after_tax_max?: string;
    bankaccount?: number;
    costaccount?: number;
    purchasetaxaccount?: number;
    costcentre?: number;
    foreign_business_base?: number;
    country_dep?: string;
    country_rec?: string;
    tag?: number;
    vatin?: string;
    vatin_contains?: string;
    description?: string;
    description_contains?: string;
    create_date?: string;
    create_date_from?: string;
    create_date_until?: string;
    update_date?: string;
    update_date_from?: string;
    update_date_until?: string;
    has_attachments?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<Booking>> {
    const response = await this.client.get("bookings/", { params });
    return response.data;
  }

  async getOpenBookings(params?: {
    page?: number;
    [key: string]: any;
  }): Promise<ApiResponse<Booking>> {
    const response = await this.client.get("bookings/open/", { params });
    return response.data;
  }

  async getDeletedBookings(params?: {
    page?: number;
    [key: string]: any;
  }): Promise<ApiResponse<Booking>> {
    const response = await this.client.get("bookings/deleted/", { params });
    return response.data;
  }

  async getImportedBookings(params?: {
    page?: number;
    [key: string]: any;
  }): Promise<ApiResponse<Booking>> {
    const response = await this.client.get("bookings/imported/", { params });
    return response.data;
  }

  async getBooking(id: number): Promise<Booking> {
    const response = await this.client.get(`bookings/${id}/`);
    return response.data;
  }

  async createBooking(booking: CreateBooking): Promise<Booking> {
    const response = await this.client.post("bookings/", booking);
    return response.data;
  }
}
