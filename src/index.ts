#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import express from "express";
import { randomUUID } from "node:crypto";
import { BookamatApiClient } from "./api-client.js";
import { getConfig, validateConfig } from "./config.js";
import { registerAllTools } from "./tools/index.js";
import 'dotenv/config'

/**
 * Bookamat MCP Server
 * 
 * This server provides a Model Context Protocol (MCP) interface to the Bookamat accounting system.
 * It allows clients to interact with Bookamat's accounting features through a standardized API.
 */

// Initialize configuration and API client
let apiClient: BookamatApiClient;

try {
  const config = getConfig();
  validateConfig(config);
  apiClient = new BookamatApiClient(config);
} catch (error) {
  console.error("Configuration error:", error instanceof Error ? error.message : error);
  process.exit(1);
}

// Create server instance
const server = new McpServer({
  name: "bookamat",
  version: "1.0.0",
  capabilities: {
    resources: {},
    tools: {},
  },
});

// Register all tools from the tools directory
registerAllTools(server, apiClient);

// Determine transport mode from command line arguments or environment
const args = process.argv.slice(2);
const useHttp = args.includes('--http') || process.env.BOOKAMAT_HTTP === 'true';
const port = parseInt(process.env.BOOKAMAT_PORT || '8080');

if (useHttp) {
  // HTTP Server Mode
  startHttpServer();
} else {
  // Stdio Mode (default)
  startStdioServer();
}

async function startStdioServer() {
  try {
    console.error("Starting Bookamat MCP Server in stdio mode...");

    // Test API connection
    await apiClient.getBankAccounts({ page: 1 });
    console.error("✓ Bookamat API connection successful");

    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error("✓ Bookamat MCP Server started successfully (stdio)");
    console.error("Available tools: list_bank_accounts, get_bank_account, list_cost_accounts, get_cost_account, list_purchase_tax_accounts, get_purchase_tax_account, list_cost_centers, get_cost_center, list_bookings, get_booking, create_booking");

  } catch (error) {
    console.error("Failed to start Bookamat MCP Server:", error);
    process.exit(1);
  }
}

async function startHttpServer() {
  try {
    console.error(`Starting Bookamat MCP Server in HTTP mode on port ${port}...`);

    // Test API connection
    await apiClient.getBankAccounts({ page: 1 });
    console.error("✓ Bookamat API connection successful");

    const app = express();
    app.use(express.json());

    // Map to store transports by session ID
    const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

    // Health check endpoint
    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'bookamat-mcp',
        version: '1.0.0',
        timestamp: new Date().toISOString()
      });
    });

    // MCP endpoint - handle POST requests for client-to-server communication
    app.post('/mcp', async (req, res) => {
      try {
        // Check for existing session ID
        const sessionId = req.headers['mcp-session-id'] as string | undefined;
        let transport: StreamableHTTPServerTransport;

        if (sessionId && transports[sessionId]) {
          // Reuse existing transport
          transport = transports[sessionId];
        } else {
          // Create new transport for new session
          transport = new StreamableHTTPServerTransport({
            sessionIdGenerator: () => randomUUID(),
            onsessioninitialized: (sessionId) => {
              // Store the transport by session ID
              transports[sessionId] = transport;
              console.error(`New MCP session initialized: ${sessionId}`);
            },
          });

          // Clean up transport when closed
          transport.onclose = () => {
            if (transport.sessionId) {
              console.error(`MCP session closed: ${transport.sessionId}`);
              delete transports[transport.sessionId];
            }
          };

          // Connect the server to the transport
          await server.connect(transport);
        }

        // Handle the request
        await transport.handleRequest(req, res);

      } catch (error) {
        console.error('Error handling MCP request:', error);
        if (!res.headersSent) {
          res.status(500).json({
            jsonrpc: '2.0',
            error: {
              code: -32603,
              message: 'Internal server error',
            },
            id: null,
          });
        }
      }
    });

    // MCP endpoint - handle GET requests for server-to-client communication
    app.get('/mcp', async (req, res) => {
      try {
        const sessionId = req.headers['mcp-session-id'] as string | undefined;
        if (!sessionId || !transports[sessionId]) {
          res.status(400).json({ error: 'Invalid or missing session ID' });
          return;
        }

        const transport = transports[sessionId];
        await transport.handleRequest(req, res);

      } catch (error) {
        console.error('Error handling MCP GET request:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Internal server error' });
        }
      }
    });

    // Start HTTP server
    const httpServer = app.listen(port, '0.0.0.0', () => {
      console.error(`✓ Bookamat MCP Server started successfully (HTTP)`);
      console.error(`✓ Server listening on http://0.0.0.0:${port}`);
      console.error(`✓ MCP endpoint: http://0.0.0.0:${port}/mcp`);
      console.error(`✓ Health check: http://0.0.0.0:${port}/health`);
      console.error("Available tools: list_bank_accounts, get_bank_account, list_cost_accounts, get_cost_account, list_purchase_tax_accounts, get_purchase_tax_account, list_cost_centers, get_cost_center, list_bookings, get_booking, create_booking");
    });

    // Store server reference for cleanup
    (global as any).httpServer = httpServer;

  } catch (error) {
    console.error("Failed to start Bookamat MCP Server:", error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.error("Shutting down Bookamat MCP Server...");

  // Close HTTP server if running
  if ((global as any).httpServer) {
    (global as any).httpServer.close();
  }

  await server.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error("Shutting down Bookamat MCP Server...");

  // Close HTTP server if running
  if ((global as any).httpServer) {
    (global as any).httpServer.close();
  }

  await server.close();
  process.exit(0);
});