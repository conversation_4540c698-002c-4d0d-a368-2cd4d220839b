#!/usr/bin/env node

/**
 * Simple test script to verify HTTP endpoints work
 * This script tests the HTTP server functionality without requiring valid API credentials
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8080';

async function testHealthEndpoint() {
  try {
    console.log('Testing health endpoint...');
    const response = await fetch(`${BASE_URL}/health`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Health endpoint working:', data);
      return true;
    } else {
      console.log('❌ Health endpoint failed:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.log('❌ Health endpoint error:', error.message);
    return false;
  }
}

async function testMcpEndpoint() {
  try {
    console.log('Testing MCP endpoint...');
    
    // Test with a simple MCP initialize request
    const mcpRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {},
        clientInfo: {
          name: 'test-client',
          version: '1.0.0'
        }
      }
    };
    
    const response = await fetch(`${BASE_URL}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mcpRequest)
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ MCP endpoint responding:', data);
      return true;
    } else {
      console.log('❌ MCP endpoint failed:', response.status, response.statusText);
      const text = await response.text();
      console.log('Response:', text);
      return false;
    }
  } catch (error) {
    console.log('❌ MCP endpoint error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🧪 Testing Bookamat MCP HTTP Server');
  console.log('=====================================');
  
  // Wait a moment for server to start
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const healthOk = await testHealthEndpoint();
  console.log('');
  
  const mcpOk = await testMcpEndpoint();
  console.log('');
  
  if (healthOk && mcpOk) {
    console.log('🎉 All HTTP endpoints are working correctly!');
    process.exit(0);
  } else {
    console.log('⚠️  Some endpoints may not be working. Check server logs.');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Test script error:', error);
  process.exit(1);
});
